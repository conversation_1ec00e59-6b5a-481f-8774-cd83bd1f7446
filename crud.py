from sqlalchemy.orm import Session
from database import Customer
from schemas import CustomerCreate, CustomerUpdate
from typing import List, Optional

def get_customer(db: Session, customer_id: int) -> Optional[Customer]:
    """Get a single customer by ID"""
    return db.query(Customer).filter(Customer.id == customer_id).first()

def get_customers(db: Session, skip: int = 0, limit: int = 100) -> List[Customer]:
    """Get all customers with pagination"""
    return db.query(Customer).order_by(Customer.id).offset(skip).limit(limit).all()

def create_customer(db: Session, customer: CustomerCreate) -> Customer:
    """Create a new customer"""
    db_customer = Customer(
        firstname=customer.firstname,
        lastname=customer.lastname,
        email=customer.email,
        phone=customer.phone
    )
    db.add(db_customer)
    db.commit()
    db.refresh(db_customer)
    return db_customer

def update_customer(db: Session, customer_id: int, customer_update: CustomerUpdate) -> Optional[Customer]:
    """Update an existing customer"""
    db_customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if db_customer:
        update_data = customer_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_customer, field, value)
        db.commit()
        db.refresh(db_customer)
    return db_customer

def delete_customer(db: Session, customer_id: int) -> bool:
    """Delete a customer"""
    db_customer = db.query(Customer).filter(Customer.id == customer_id).first()
    if db_customer:
        db.delete(db_customer)
        db.commit()
        return True
    return False

def get_customer_by_email(db: Session, email: str) -> Optional[Customer]:
    """Get customer by email"""
    return db.query(Customer).filter(Customer.email == email).first() 