from pydantic import BaseModel, EmailStr
from typing import Optional

# Base customer schema
class CustomerBase(BaseModel):
    firstname: str
    lastname: str
    email: str
    phone: Optional[str] = None

# Schema for creating customer
class CustomerCreate(CustomerBase):
    pass

# Schema for updating customer
class CustomerUpdate(BaseModel):
    firstname: Optional[str] = None
    lastname: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None

# Schema for reading customer (includes id)
class Customer(CustomerBase):
    id: int
    
    class Config:
        from_attributes = True 