from pydantic import BaseModel, EmailStr
from typing import Optional

# Base customer schema
class CustomerBase(BaseModel):
    firstname: str
    lastname: str
    email: str
    phone: Optional[str] = None

# Schema for creating customer
class CustomerCreate(CustomerBase):
    pass

# Schema for updating customer
class CustomerUpdate(BaseModel):
    firstname: Optional[str] = None
    lastname: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None

# Schema for reading customer (includes id)
class Customer(CustomerBase):
    id: int

    class Config:
        from_attributes = True

# Base entity schema
class EntityBase(BaseModel):
    entity_no: str
    name: str
    type: str
    country_code: str
    country_name: str
    organization_type: str
    custom_organization_type_id: str
    owner_type: str
    entity_first_name: str
    entity_last_name: str
    operating_name: str
    year_end_month: int
    incorporation_date: str
    inactive_date: Optional[str] = None

# Schema for creating entity
class EntityCreate(EntityBase):
    pass

# Schema for updating entity
class EntityUpdate(BaseModel):
    entity_no: Optional[str] = None
    name: Optional[str] = None
    type: Optional[str] = None
    country_code: Optional[str] = None
    country_name: Optional[str] = None
    organization_type: Optional[str] = None
    custom_organization_type_id: Optional[str] = None
    owner_type: Optional[str] = None
    entity_first_name: Optional[str] = None
    entity_last_name: Optional[str] = None
    operating_name: Optional[str] = None
    year_end_month: Optional[int] = None
    incorporation_date: Optional[str] = None
    inactive_date: Optional[str] = None

# Schema for reading entity (includes id)
class Entity(EntityBase):
    id: int

    class Config:
        from_attributes = True