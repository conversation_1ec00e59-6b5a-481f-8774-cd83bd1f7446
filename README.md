# Customer API

A simple FastAPI application for managing customers with CRUD operations connected to SQL Server.

## Features

- Create, Read, Update, Delete customers
- Customer fields: id, firstname, lastname, email, phone
- SQL Server database integration
- Automatic API documentation with Swagger UI
- Data validation with Pydantic

## Prerequisites

- Python 3.8+
- SQL Server Express running on localhost
- ODBC Driver 17 for SQL Server

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure SQL Server Express is running and create a database called `CustomerDB`:
```sql
CREATE DATABASE CustomerDB;
```

3. Run the application:
```bash
python main.py
```

Or using uvicorn directly:
```bash
uvicorn main:app --reload
```

## API Endpoints

The application will be available at `http://localhost:8000`

- **GET** `/` - Welcome message
- **POST** `/customers/` - Create a new customer
- **GET** `/customers/` - Get all customers (with pagination)
- **GET** `/customers/{customer_id}` - Get customer by ID
- **PUT** `/customers/{customer_id}` - Update customer
- **DELETE** `/customers/{customer_id}` - Delete customer

## API Documentation

Once running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Example Usage

### Create a customer:
```bash
curl -X POST "http://localhost:8000/customers/" \
     -H "Content-Type: application/json" \
     -d '{
       "firstname": "John",
       "lastname": "Doe", 
       "email": "<EMAIL>",
       "phone": "555-1234"
     }'
```

### Get all customers:
```bash
curl -X GET "http://localhost:8000/customers/"
```

### Update a customer:
```bash
curl -X PUT "http://localhost:8000/customers/1" \
     -H "Content-Type: application/json" \
     -d '{
       "phone": "555-5678"
     }'
```

### Delete a customer:
```bash
curl -X DELETE "http://localhost:8000/customers/1"
``` 