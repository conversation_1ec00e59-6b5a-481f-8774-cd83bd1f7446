from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List

import crud
import schemas
from database import get_db, create_tables

# Create FastAPI app
app = FastAPI(
    title="Customer API",
    description="A simple CRUD API for customer management",
    version="1.0.0"
)

# Create tables on startup
@app.on_event("startup")
def startup_event():
    create_tables()

# Root endpoint
@app.get("/")
def read_root():
    return {"message": "Welcome to Customer API"}

# Create customer
@app.post("/customers/", response_model=schemas.Customer)
def create_customer(
    customer: schemas.CustomerCreate, 
    db: Session = Depends(get_db)
):
    # Check if email already exists
    existing_customer = crud.get_customer_by_email(db, email=customer.email)
    if existing_customer:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    return crud.create_customer(db=db, customer=customer)

# Get all customers
@app.get("/customers/", response_model=List[schemas.Customer])
def read_customers(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db)
):
    customers = crud.get_customers(db, skip=skip, limit=limit)
    return customers

# Get customer by ID
@app.get("/customers/{customer_id}", response_model=schemas.Customer)
def read_customer(customer_id: int, db: Session = Depends(get_db)):
    db_customer = crud.get_customer(db, customer_id=customer_id)
    if db_customer is None:
        raise HTTPException(status_code=404, detail="Customer not found")
    return db_customer

# Update customer
@app.put("/customers/{customer_id}", response_model=schemas.Customer)
def update_customer(
    customer_id: int,
    customer_update: schemas.CustomerUpdate,
    db: Session = Depends(get_db)
):
    # Check if customer exists
    existing_customer = crud.get_customer(db, customer_id=customer_id)
    if not existing_customer:
        raise HTTPException(status_code=404, detail="Customer not found")
    
    # Check if email is being updated and already exists
    if customer_update.email:
        email_customer = crud.get_customer_by_email(db, email=customer_update.email)
        if email_customer and email_customer.id != customer_id:
            raise HTTPException(status_code=400, detail="Email already registered")
    
    updated_customer = crud.update_customer(db, customer_id=customer_id, customer_update=customer_update)
    return updated_customer

# Delete customer
@app.delete("/customers/{customer_id}")
def delete_customer(customer_id: int, db: Session = Depends(get_db)):
    success = crud.delete_customer(db, customer_id=customer_id)
    if not success:
        raise HTTPException(status_code=404, detail="Customer not found")
    return {"message": "Customer deleted successfully"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 